import admin from "../../../../lib/firebase-admin.js";

export default async function handler(req, res) {

    const weather = (lat, lon) => fetch(`https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${process.env.OPENWEATHERMAP_API_KEY}`).then((res) => res.json());

    const sitesCollection = admin.firestore().collection("sites");
    const sites = await sitesCollection.get();
    const sitesData = sites.docs.map(
        async (doc) => {
            const data = doc.data();
            const coordinates = data.properties.coordinates;
            return weather(coordinates[1], coordinates[0]).then((weather) => {
                return {
                    id: doc.id,
                    weather: weather.weather[0].main,
                };
            });
        }
    );



}
