import { demoIssues } from "../../../src/service/demo/client-demo-data";

export default async function handler(req, res) {

    if (req.headers.authorization !== process.env.API_KEY) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    if (req.query.mode === "demo") {
        switch (req.method) {
            case 'GET':
                return res.status(200).json(demoIssues);
            case 'POST':
                var issues = req.body;
                demoIssues.push({
                    id: `issueId-${demoIssues.length + 1}`,
                    ...issues,
                });
                return res.status(200).json({
                    message: "Issue created",
                    id: `issueId-${demoIssues.length}`,
                });
            default:
                return res.status(405).end(`Method ${req.method} Not Allowed`);
        }
    }
    switch (req.method) {
        case 'GET':
            return res.status(200).json({ message: 'Hello World' });
        default:
            return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}