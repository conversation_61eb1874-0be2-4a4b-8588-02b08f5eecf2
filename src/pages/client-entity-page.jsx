import { useOutletContext, useParams } from "react-router-dom";
import AppPageTitle from "../components/ui/app-page-title";
import {
  Colors,
  CompoundTag,
  EntityTitle,
  H4,
  Tab,
  TabPanel,
  Tabs,
  Tag,
} from "@blueprintjs/core";
import { functions } from "../service/functions";
import { useRef, useState } from "react";

export default function ClientEntityPage() {
  const { entities, sites, theme, feed } = useOutletContext();
  const { id } = useParams();
  const [activeTabId, setActiveTabId] = useState("overview");

  const feeds = feed
    .filter((f) => f.tenantId === id)
    .sort((a, b) => {
      return b.createdAt - a.createdAt;
    });
  const entity = entities.find((e) => e.id === id);
  return (
    <>
      <AppPageTitle
        title={entity?.name}
        actions={
          <CompoundTag
            intent={functions.tools.intentConfidence(5)}
            size="large"
            leftContent="Confidence"
          >
            {5}
          </CompoundTag>
        }
      />
      <div style={{ padding: "0px 20px 0px 40px" }}>
        <Tabs fill selectedTabId={activeTabId} onChange={setActiveTabId}>
          <Tab id="overview" title="Overview" />
          <Tab id="sites" title="Sites" />
          <Tab id="activities" title="Activities" />
          <Tab id="issues" title="Issues" />
          <Tab id="feed" title="Feed" />
          <Tab id="members" title="Members" />
        </Tabs>
      </div>
      <div
        style={{
          padding: "0px 0px 0px 40px",
          display: "flex",
          flex: 1,
          borderTop: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : "rgba(255, 255, 255, 0.2)"
          }`,
        }}
      >
        <div
          style={{
            flex: 1,
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <TabPanel
            id={"overview"}
            style={{ overflow: "hidden", height: "100%" }}
            selectedTabId={activeTabId}
            key={"overview"}
            panel={
              <div
                style={{ minHeight: "100%", backgroundColor: "red", flex: 1 }}
              >
                Overview
              </div>
            }
          />
        </div>
        <div
          style={{
            width: 300,
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
            gap: 10,
            paddingBlock: 30,
            paddingInline: 20,
            backgroundColor:
              theme === "light" ? Colors.LIGHT_GRAY5 : Colors.DARK_GRAY3,
            borderLeft: `1px solid ${
              theme === "light"
                ? Colors.LIGHT_GRAY1
                : "rgba(255, 255, 255, 0.2)"
            }`,
          }}
        >
          <EntityTitle title="Sites" heading={H4} />
          {sites
            .filter((s) => s.entity === id)
            .map((s) => (
              <div key={s.id}>
                <EntityTitle
                  fill
                  title={s.name}
                  subtitle={s.code}
                  tags={
                    <Tag
                      intent={functions.tools.intentConfidence(
                        s.confidenceRating
                          ? functions.tools.calculateConfidenceScore(
                              s.confidenceRating
                            ) * 10
                          : 0
                      )}
                    >
                      {s.confidenceRating
                        ? functions.tools.calculateConfidenceScore(
                            s.confidenceRating
                          )
                        : 0}
                    </Tag>
                  }
                />
              </div>
            ))}
        </div>
      </div>
    </>
  );
}
