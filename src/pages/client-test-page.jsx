import { useOutletContext } from "react-router-dom";
import { functions } from "../service/functions";
import dayjs from "dayjs";

export default function ClientTestPage() {
  const { entities, sites } = useOutletContext();
  return (
    <div>
      {sites
        .filter((s) => s.confidenceRating !== undefined)
        .map((s) => (
          <div key={s.id}>
            <div>{s.name}</div>
            <div>
              Score:{" "}
              {functions.tools.calculateConfidenceScore(s.confidenceRating)}
              <div>
                Failure probability:
                {
                  functions.tools.simulateConfidenceMonteCarlo(
                    s.confidenceRating,
                    1440,
                    1000,
                    0.55
                  ).failureProbability
                }
              </div>
            </div>
          </div>
        ))}
    </div>
  );
}
