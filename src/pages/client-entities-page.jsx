import { useNavigate, useOutletContext } from "react-router-dom";
import AppPageTitle from "../components/ui/app-page-title";
import {
  Button,
  Card,
  CompoundTag,
  EntityTitle,
  H4,
  H5,
} from "@blueprintjs/core";

export default function ClientEntitiesPage() {
  const { entities, sites } = useOutletContext();

  const navigate = useNavigate();
  return (
    <>
      <AppPageTitle
        title="Entities"
        actions={<Button icon="add" text="Add entity" intent="primary" />}
      />
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(3, 1fr)",
          gap: "20px",
          padding: "0px 20px 0px 40px",
          boxSizing: "border-box",
          flexWrap: "wrap",
        }}
      >
        {entities.map((e) => (
          <Card
            onClick={() => navigate(`/entity/${e.id}`)}
            interactive
            key={e.id}
            compact
            style={{
              minWidth: "240px", // pour éviter que la carte devienne trop étroite
            }}
          >
            <EntityTitle
              title={e.name}
              ellipsize
              heading={H5}
              fill
              subtitle={e.country}
              tags={<CompoundTag leftContent="Confidence">{0}</CompoundTag>}
            />
            <div style={{ paddingTop: 15, display: "flex", gap: 15 }}>
              <EntityTitle
                heading={H4}
                subtitle={"Sites"}
                title={sites.filter((s) => s.entity === e.id).length}
              />
              <EntityTitle
                heading={H4}
                subtitle={"Members"}
                title={e.members.length}
              />
            </div>
          </Card>
        ))}
      </div>
    </>
  );
}
