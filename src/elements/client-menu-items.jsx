import { useNavigate } from "react-router-dom";
import ClientDashboardPage from "../pages/client-dashboard-page";
import ClientEntitiesPage from "../pages/client-entities-page";
import ClientEntityPage from "../pages/client-entity-page";
import ClientMapPage from "../pages/client-map-page";
import ClientTestPage from "../pages/client-test-page";

export const clientMenuItems = [
  {
    title: "Dashboard",
    path: "/",
    icon: "dashboard",
    id: "dashboard",
    render: <ClientDashboardPage />,
    count: false,
    visible: true,
    intent: window.location.pathname === "/" ? "primary" : "none",
  },
  {
    title: "Map view",
    path: "/map",
    icon: "map",
    id: "map",
    render: <ClientMapPage />,
    count: false,
    visible: true,
    intent: window.location.pathname === "/map" ? "primary" : "none",
  },
  {
    title: "Entities",
    path: "/entities",
    icon: "office",
    id: "entities",
    render: <ClientEntitiesPage />,
    count: true,
    visible: true,
    intent: window.location.pathname.startsWith("/entities")
      ? "primary"
      : "none",
  },
  {
    title: "Entity",
    path: "/entity/:id",
    icon: "office",
    id: "entity",
    render: <ClientEntityPage />,
    count: false,
    visible: false,
    intent: window.location.pathname.startsWith("/entity") ? "primary" : "none",
  },
  {
    title: "Sites",
    path: "/sites",
    icon: "map-marker",
    id: "sites",
    render: "Sites",
    count: true,
    visible: true,
    intent: window.location.pathname.startsWith("/sites") ? "primary" : "none",
  },
  {
    title: "Test",
    path: "/test",
    icon: "lab-test-tube",
    id: "test",
    render: <ClientTestPage />,
    count: false,
    visible: false,
    intent: window.location.pathname === "/test" ? "primary" : "none",
  },
];
