import { useEffect, useState } from "react";
import { Outlet, useOutletContext } from "react-router-dom";
import {
  demoEntitiesClient,
  demoFeed,
  demoSitesClient,
} from "../../service/demo/client-demo-data";
import ClientFormsAddIssue from "../forms/cllient-forms-add-issue";
import { functions } from "../../service/functions";

export default function ClientContext() {
  const [demoMode, setDemoMode] = useState(
    localStorage.getItem("demoMode") === "true"
  );
  const [sites, setSites] = useState([]);
  const [entities, setEntities] = useState([]);
  const [feed, setFeed] = useState([]);
  const [issues, setIssues] = useState([]);
  const [openIssue, setOpenIssue] = useState(true);
  const { user, theme, sidebarOpen } = useOutletContext();

  useEffect(() => {
    if (demoMode) {
      setEntities(demoEntitiesClient);
      setSites(demoSitesClient);
      setFeed(demoFeed);
      functions.demo.async.listIssues().then((issues) => setIssues(issues));
    } else {
      null;
    }
  }, [demoMode]);

  return (
    <>
      <ClientFormsAddIssue open={openIssue} setOpen={setOpenIssue} />
      <Outlet
        context={{
          entities,
          sites,
          user,
          theme,
          sidebarOpen,
          demoMode,
          setDemoMode,
          feed,
          issues,
        }}
      />
    </>
  );
}
