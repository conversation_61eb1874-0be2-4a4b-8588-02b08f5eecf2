import { useEffect, useState } from "react";
import GlobalLoader from "../loader/global-loader";
import {
  Button,
  Colors,
  EntityTitle,
  Menu,
  MenuDivider,
  MenuItem,
  Popover,
} from "@blueprintjs/core";
import dayjs from "dayjs";
import Logo from "../assets/logo";
import { Outlet, useParams, useSearchParams } from "react-router-dom";

export default function GlobalLayout() {
  const [user, setUser] = useState(null);
  const [theme, setTheme] = useState(
    window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "bp6-dark"
      : "light"
  );
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [searchParams, setSearchParams] = useSearchParams();

  window.matchMedia("(prefers-color-scheme: dark)").onchange = (e) => {
    setTheme(e.matches ? "bp6-dark" : "light");
  };

  return (
    <>
      <div
        className={theme}
        style={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100svh",
          maxHeight: "100svh",
          overflow: "hidden",
          backgroundColor: theme === "light" ? "white" : Colors.DARK_GRAY2,
          borderTop: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY1
          }`,
        }}
      >
        {localStorage.getItem("demoMode") === "true" && (
          <div
            style={{
              backgroundColor: "#af007c",
              color: "white",
              paddingInline: 15,
              paddingBlock: 10,
              fontFamily: "monospace",
              fontSize: 10,
              display: "flex",
              alignItems: "center",
            }}
          >
            DEMO MODE
          </div>
        )}
        <div
          style={{
            height: 50,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div
            style={{
              height: 50,
              width: sidebarOpen ? 230 : 50,
              display: "flex",
              alignItems: "center",
              paddingInline: 15,
              borderRight: `1px solid ${
                theme === "light"
                  ? Colors.LIGHT_GRAY1
                  : "rgba(255, 255, 255, 0.2)"
              }`,
              justifyContent: "space-between",
            }}
          >
            {sidebarOpen && <Logo />}
            <Button
              icon={sidebarOpen ? "menu-closed" : "menu-open"}
              size="small"
              variant="minimal"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            />
          </div>
          <div
            style={{
              flex: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              paddingInline: "40px 15px",
            }}
          >
            <div>{dayjs().format("dddd MMMM D YYYY")}</div>
            <div>
              <Button
                icon={theme === "light" ? "moon" : "flash"}
                variant="minimal"
                onClick={() =>
                  setTheme(theme === "light" ? "bp6-dark" : "light")
                }
              />
              <Button icon="cog" variant="minimal" />
              <Button icon="notifications" variant="minimal" />
              <Popover
                content={
                  <Menu>
                    <MenuDivider
                      title={
                        <EntityTitle
                          title="User"
                          subtitle={"<EMAIL>"}
                        />
                      }
                    />
                    <MenuDivider />
                    <MenuItem
                      icon="rocket"
                      text="Demo mode"
                      onClick={() => {
                        localStorage.getItem("demoMode") === "true"
                          ? localStorage.setItem("demoMode", "false")
                          : localStorage.setItem("demoMode", "true");
                        window.location.replace("/");
                      }}
                    />
                  </Menu>
                }
              >
                <Button icon="user" variant="minimal" />
              </Popover>
            </div>
          </div>
        </div>
        <div style={{ flex: 1, overflow: "hidden", display: "flex" }}>
          <Outlet context={{ user, theme, sidebarOpen }} />
        </div>
      </div>
    </>
  );
}
