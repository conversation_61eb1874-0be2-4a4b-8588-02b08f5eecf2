import {
  Button,
  ButtonGroup,
  <PERSON>alog,
  DialogBody,
  DialogFooter,
  EditableText,
  FileInput,
} from "@blueprintjs/core";

export default function ClientFormsAddIssue({ open, setOpen }) {
  return (
    <>
      <Dialog
        isOpen={open}
        icon="issue-new"
        autoFocus
        onClose={() => setOpen(false)}
        hasBackdrop={false}
        title="New issue"
      >
        <form>
          <DialogBody
            style={{ display: "flex", flexDirection: "column", gap: 15 }}
          >
            <div style={{ fontSize: 18, fontWeight: 700, width: "100%" }}>
              <EditableText
                autoFocus
                minWidth={"100%"}
                placeholder="Issue title"
                multiline
                maxLines={1}
              />
            </div>
            <div style={{ fontSize: 14, width: "100%" }}>
              <EditableText
                minWidth={"100%"}
                placeholder="Issue description"
                multiline
                minLines={3}
              />
            </div>
            <ButtonGroup size="small" variant="outlined">
              <Button text="State" />
              <Button text="Priority" />
              <Button text="Assignee" />
            </ButtonGroup>
          </DialogBody>
          <DialogFooter
            actions={[
              <Button intent="primary" text="Create issue" key={"create"} />,
            ]}
          >
            <FileInput />
          </DialogFooter>
        </form>
      </Dialog>
    </>
  );
}
