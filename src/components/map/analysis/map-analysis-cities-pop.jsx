import { useEffect, useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import { functions } from "../../../service/functions";
import { Colors } from "@blueprintjs/core";

export default function MapAnalysisCitiesPop({
  mapRef,
  center,
  radius = 200,
  mode = "circle",
}) {
  const [cities, setCities] = useState(null);

  useEffect(() => {
    if (center) {
      functions.data.geo.gacwp(center[0], center[1], radius).then((data) => {
        const processedCities = data.features.results.map((feature) => ({
          coordinates: [feature.coordinates.lon, feature.coordinates.lat],
          population: feature.population,
          name: feature.name,
        }));
        console.log("Cities data:", processedCities);
        setCities(processedCities);
      });
    }
  }, [center, radius]);

  if (!mapRef.current) return null;
  return (
    <>
      {cities && (
        <Source
          type="geojson"
          data={{
            type: "FeatureCollection",
            features: cities.map((city) => {
              // Créer un cercle dont la taille varie selon la population

              return {
                type: "Feature",
                properties: {
                  population: city.population,
                  name: city.name,
                },
                geometry: {
                  type: "Point",
                  coordinates: city.coordinates,
                },
              };
            }),
          }}
        >
          <Layer
            type="heatmap"
            paint={{
              "heatmap-radius": [
                "interpolate",
                ["linear"],
                ["get", "population"],
                Math.min(...cities.map((city) => city.population)),
                10,
                Math.max(...cities.map((city) => city.population)),
                40,
              ],
              "heatmap-weight": [
                "interpolate",
                ["linear"],
                ["get", "population"],
                0,
                0,
                1000,
                0.2,
                5000,
                0.5,
                20000,
                1,
                50000,
                1.5,
                100000,
                2,
              ],
              "heatmap-intensity": [
                "interpolate",
                ["linear"],
                ["zoom"],
                0,
                1,
                9,
                3,
              ],
              "heatmap-color": [
                "interpolate",
                ["linear"],
                ["heatmap-density"],
                0,
                "rgba(33,102,172,0)",
                0.2,
                "rgb(103,169,207)",
                0.4,
                "rgb(209,229,240)",
                0.6,
                "rgb(253,219,199)",
                0.8,
                "rgb(239,138,98)",
                1,
                "rgb(178,24,43)",
              ],
              "heatmap-opacity": 0.6,
            }}
          />
        </Source>
      )}
    </>
  );
}
