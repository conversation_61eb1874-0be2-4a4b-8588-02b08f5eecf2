import { useEffect, useState } from "react";
import Map from "react-map-gl/mapbox";
import MapDrawControl from "./control/map-draw-control";
import MapGeneralControl from "./control/map-general-control";
import MapSearchControl from "./control/map-search-control";
import ClientSitesMarkers from "./markers/client-sites-markers";
import { useOutletContext } from "react-router-dom";

export default function LightMap({ mapRef }) {
  const [mapStyle, setMapStyle] = useState(
    "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
  );
  const { sites, theme, sidebarOpen } = useOutletContext();

  useEffect(() => {
    setMapStyle(
      theme === "light"
        ? "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
        : "mapbox://styles/tsinovec/cmdrfkyi5008801r5ckbg9zvf"
    );
  }, [theme]);

  useEffect(() => {
    if (mapRef.current) mapRef.current.resize();
  }, [sidebarOpen]);

  return (
    <>
      <Map
        style={{ height: "100%" }}
        onLoad={(target) => {
          mapRef.current = target.target;
        }}
        minZoom={2}
        ref={mapRef}
        logoPosition="bottom-right"
        attributionControl={false}
        mapboxAccessToken={import.meta.env.VITE_MAPBOX_TOKEN}
        mapStyle={mapStyle}
      ></Map>
    </>
  );
}
