import { SearchBox } from "@mapbox/search-js-react";
import { useState } from "react";
import { Button, Colors, EntityTitle, H4, H5 } from "@blueprintjs/core";
import MapAnalysisCitiesPop from "../analysis/map-analysis-cities-pop";

export default function MapSearchControl({
  mapRef,
  center,
  population = false,
}) {
  const [retrieve, setRetrieve] = useState(null);
  return (
    <>
      <div
        style={{
          position: "absolute",
          top: 20,
          left: 40,
          display: "flex",
          flexFlow: "column",
          gap: 15,
        }}
      >
        <SearchBox
          onClear={() => {
            setRetrieve(null);
          }}
          onRetrieve={(r) => {
            setRetrieve(r);
            mapRef.current?.flyTo({
              center: r.features[0].geometry.coordinates,
              zoom: 10,
              speed: 1,
            });
          }}
          theme={{
            variables: {
              boxShadow:
                "0 0 0 0 rgba(33,93,176,0),0 0 0 0 rgba(33,93,176,0),inset 0 0 0 1px rgba(17,20,24,.2),inset 0 1px 1px rgba(17,20,24,.3)",
              borderRadius: "2px",
            },
          }}
          accessToken={import.meta.env.VITE_MAPBOX_TOKEN}
          options={{
            proximity: {
              lat: center?.lat || 0,
              lng: center?.lng || 0,
            },
          }}
        />
      </div>
      {population && retrieve && (
        <MapAnalysisCitiesPop
          mapRef={mapRef}
          center={retrieve.features[0].geometry.coordinates}
          radius={100}
        />
      )}
    </>
  );
}
