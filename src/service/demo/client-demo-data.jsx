import dayjs from "dayjs";

export const demoEntitiesClient = [
  {
    id: `entityId-1`,
    name: "Global Swift France",
    country: "France",
    description: "This is entity 1",
    members: [
      "memberId-1",
      "memberId-2",
      "memberId-3",
      "memberId-4",
      "memberId-5",
    ],
    createdAt: dayjs().subtract(1, "day").toDate(),
    updatedAt: dayjs().subtract(1, "hour").toDate(),
  },
  {
    id: `entityId-2`,
    name: "Global Swift Spain",
    country: "Spain",
    description: "This is entity 2",
    members: ["memberId-1", "memberId-2", "memberId-3", "memberId-4"],
    createdAt: dayjs().subtract(2, "day").toDate(),
    updatedAt: dayjs().subtract(2, "hour").toDate(),
  },
  {
    id: `entityId-3`,
    name: "Global Swift Italy",
    country: "Italy",
    description: "This is entity 3",
    members: [
      "memberId-1",
      "memberId-2",
      "memberId-3",
      "memberId-4",
      "memberId-5",
      "memberId-6",
      "memberId-7",
      "memberId-8",
      "memberId-9",
      "memberId-10",
    ],
    createdAt: dayjs().subtract(3, "day").toDate(),
    updatedAt: dayjs().subtract(3, "hour").toDate(),
  },
  {
    id: `entityId-4`,
    name: "Global Swift Germany",
    country: "Germany",
    description: "This is entity 4",
    members: ["memberId-1", "memberId-2"],
    createdAt: dayjs().subtract(4, "day").toDate(),
    updatedAt: dayjs().subtract(4, "hour").toDate(),
  },
  {
    id: `entityId-5`,
    name: "Global Swift UK",
    country: "UK",
    description: "This is entity 5",
    members: ["memberId-1", "memberId-2"],
    createdAt: dayjs().subtract(5, "day").toDate(),
    updatedAt: dayjs().subtract(5, "hour").toDate(),
  },
];

export const demoSitesClient = [
  {
    id: `siteId-1`,
    name: "Global Swift Paris",
    code: "GSF-PAR",
    type: "Hub",
    state: "Operational",
    entity: "entityId-1",
    properties: {
      context: {
        place: {
          name: "Paris",
        },
        country: {
          name: "France",
          country_code: "FR",
        },
      },
      coordinates: [2.34839, 48.8535],
    },
    members: ["memberId-1", "memberId-2"],
    confidenceRating: {
      internalIndicator: [
        { name: "Workforces", score: 0, weighting: 0.15 },
        { name: "Systems", score: 0, weighting: 0.2 },
        { name: "Maintenance", score: 0, weighting: 0.15 },
        { name: "Security", score: 0, weighting: 0.1 },
        { name: "Governance", score: 0, weighting: 0.05 },
        { name: "Actual state", score: 0, weighting: 0.05 },
      ],
      externalIndicator: [
        { name: "Vendor activities", score: 0, weighting: 0.1 },
        { name: "Public information", score: 0, weighting: 0.1 },
        { name: "Weather", score: 0, weighting: 0.1 },
      ],
      historic: [
        {
          date: dayjs().subtract(1, "day").toDate(),
          score: {
            internalIndicator: [
              { name: "Workforces", score: 1, weighting: 0.15 },
              { name: "Systems", score: 0.8, weighting: 0.2 },
              { name: "Maintenance", score: 1, weighting: 0.15 },
              { name: "Security", score: 1, weighting: 0.1 },
              { name: "Governance", score: 1, weighting: 0.05 },
              { name: "Actual state", score: 0.9, weighting: 0.05 },
            ],
            externalIndicator: [
              { name: "Vendor activities", score: 0.6, weighting: 0.1 },
              { name: "Public information", score: 0.7, weighting: 0.1 },
              { name: "Weather", score: 0.9, weighting: 0.1 },
            ],
          },
        },
        {
          date: dayjs().subtract(2, "day").toDate(),
          score: {
            internalIndicator: [
              { name: "Workforces", score: 1, weighting: 0.15 },
              { name: "Systems", score: 0.8, weighting: 0.2 },
              { name: "Maintenance", score: 1, weighting: 0.15 },
              { name: "Security", score: 1, weighting: 0.1 },
              { name: "Governance", score: 1, weighting: 0.05 },
              { name: "Actual state", score: 0.9, weighting: 0.05 },
            ],
            externalIndicator: [
              { name: "Vendor activities", score: 0.6, weighting: 0.1 },
              { name: "Public information", score: 0.7, weighting: 0.1 },
              { name: "Weather", score: 0.9, weighting: 0.1 },
            ],
          },
        },
      ],
    },
    createdAt: dayjs().subtract(1, "day").toDate(),
    updatedAt: dayjs().subtract(1, "hour").toDate(),
  },
  {
    id: `siteId-2`,
    name: "Global Swift Lyon",
    code: "GSF-LYO",
    type: "Agency",
    state: "Operational",
    entity: "entityId-1",
    properties: {
      context: {
        place: {
          name: "Lyon",
        },
        country: {
          name: "France",
          country_code: "FR",
        },
      },
      coordinates: [4.83262, 45.7661],
    },
    members: ["memberId-1", "memberId-2"],
    confidenceRating: {
      internalIndicator: [
        { name: "Workforces", score: 1, weighting: 0.15 },
        { name: "Systems", score: 0.8, weighting: 0.2 },
        { name: "Maintenance", score: 0.78, weighting: 0.15 },
        { name: "Security", score: 1, weighting: 0.1 },
        { name: "Governance", score: 1, weighting: 0.05 },
        { name: "Actual state", score: 0.8, weighting: 0.05 },
      ],
      externalIndicator: [
        { name: "Vendor activities", score: 0.7, weighting: 0.1 },
        { name: "Public information", score: 0.8, weighting: 0.1 },
        { name: "Weather", score: 0.3, weighting: 0.1 },
      ],
      historic: [
        {
          date: dayjs().subtract(1, "day").toDate(),
          score: {
            internalIndicator: [
              { name: "Workforces", score: 1, weighting: 0.15 },
              { name: "Systems", score: 0.8, weighting: 0.2 },
              { name: "Maintenance", score: 1, weighting: 0.15 },
              { name: "Security", score: 1, weighting: 0.1 },
              { name: "Governance", score: 1, weighting: 0.05 },
              { name: "Actual state", score: 0.9, weighting: 0.05 },
            ],
            externalIndicator: [
              { name: "Vendor activities", score: 0.6, weighting: 0.1 },
              { name: "Public information", score: 0.7, weighting: 0.1 },
              { name: "Weather", score: 0.9, weighting: 0.1 },
            ],
          },
        },
        {
          date: dayjs().subtract(2, "day").toDate(),
          score: {
            internalIndicator: [
              { name: "Workforces", score: 1, weighting: 0.15 },
              { name: "Systems", score: 0.8, weighting: 0.2 },
              { name: "Maintenance", score: 1, weighting: 0.15 },
              { name: "Security", score: 1, weighting: 0.1 },
              { name: "Governance", score: 1, weighting: 0.05 },
              { name: "Actual state", score: 0.9, weighting: 0.05 },
            ],
            externalIndicator: [
              { name: "Vendor activities", score: 0.6, weighting: 0.1 },
              { name: "Public information", score: 0.7, weighting: 0.1 },
              { name: "Weather", score: 0.9, weighting: 0.1 },
            ],
          },
        },
      ],
    },
    createdAt: dayjs().subtract(2, "day").toDate(),
    updatedAt: dayjs().subtract(2, "hour").toDate(),
  },
  {
    id: `siteId-3`,
    name: "Global Swift Toulouse",
    code: "GSF-TLS",
    type: "Agency",
    state: "Operational",
    entity: "entityId-1",
    properties: {
      context: {
        place: {
          name: "Toulouse",
        },
        country: {
          name: "France",
          country_code: "FR",
        },
      },
      coordinates: [1.44571, 43.6037],
    },
    members: ["memberId-1", "memberId-2"],
    createdAt: dayjs().subtract(3, "day").toDate(),
    updatedAt: dayjs().subtract(0.5, "hour").toDate(),
  },
  {
    id: `siteId-4`,
    name: "Global Swift Bordeaux",
    code: "GSF-BOR",
    type: "Agency",
    state: "Operational",
    entity: "entityId-1",
    properties: {
      context: {
        place: {
          name: "Bordeaux",
        },
        country: {
          name: "France",
          country_code: "FR",
        },
      },
      coordinates: [-0.57918, 44.83778],
    },
    members: ["memberId-1", "memberId-2"],
    createdAt: dayjs().subtract(4, "day").toDate(),
    updatedAt: dayjs().subtract(0.5, "hour").toDate(),
  },
  {
    id: `siteId-5`,
    name: "Global Swift Marseille",
    code: "GSF-MRS",
    type: "Agency",
    state: "Operational",
    entity: "entityId-1",
    properties: {
      context: {
        place: {
          name: "Marseille",
        },
        country: {
          name: "France",
          country_code: "FR",
        },
      },
      coordinates: [5.36978, 43.29648],
    },
    members: ["memberId-1", "memberId-2"],
    createdAt: dayjs().subtract(5, "day").toDate(),
    updatedAt: dayjs().subtract(0.5, "hour").toDate(),
  },
  {
    id: `siteId-6`,
    name: "Global Swift Nice",
    code: "GSF-NCE",
    type: "Agency",
    state: "Operational",
    entity: "entityId-1",
    properties: {
      context: {
        place: {
          name: "Nice",
        },
        country: {
          name: "France",
          country_code: "FR",
        },
      },
      coordinates: [7.26444, 43.70325],
    },
    members: ["memberId-1", "memberId-2"],
    createdAt: dayjs().subtract(6, "day").toDate(),
    updatedAt: dayjs().subtract(0.5, "hour").toDate(),
  },
  {
    id: `siteId-7`,
    name: "Global Swift Nantes",
    code: "GSF-NAN",
    type: "Agency",
    state: "Operational",
    entity: "entityId-1",
    properties: {
      context: {
        place: {
          name: "Nantes",
        },
        country: {
          name: "France",
          country_code: "FR",
        },
      },
      coordinates: [-1.55472, 47.21837],
    },
    members: ["memberId-1", "memberId-2"],
    createdAt: dayjs().subtract(7, "day").toDate(),
    updatedAt: dayjs().subtract(0.5, "hour").toDate(),
  },
];

export const demoFeed = [
  {
    id: `feedId-1`,
    tenantId: "entityId-1",
    title: "New site added",
    content: "A new site has been added to the entity",
    link: "/entity/entityId-1",
    reactions: {
      likes: ["memberId-1", "memberId-2"],
      comments: [
        {
          id: "commentId-1",
          author: "memberId-1",
          content: "This is a comment",
          likes: ["memberId-2"],
          createdAt: dayjs().subtract(1, "day").toDate(),
          updatedAt: dayjs().subtract(1, "hour").toDate(),
        },
      ],
    },
    createdAt: dayjs().subtract(1, "day").toDate(),
    updatedAt: dayjs().subtract(1, "hour").toDate(),
  },
];

export const demoIssues = [
  {
    id: `issueId-1`,
    tenantId: "entityId-1",
    title: "This is an issue",
    description: "This is an issue description",
    state: "Open",
    priority: "High",
    assignee: "memberId-1",
    tags: ["tag1", "tag2"],
    files: [],
    endDate: dayjs().add(1, "day").toDate(),
    startDate: dayjs().subtract(1, "day").toDate(),
    createdAt: dayjs().subtract(1, "day").toDate(),
    updatedAt: dayjs().subtract(1, "hour").toDate(),
  },
];
