export const functions = {
  users: {
    async: {
      listUsers: async () => {
        const users = (
          await fetch(`${location.origin}/api/v1/users`, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
          })
        ).json();
        return users;
      },
      getUser: async (id) => {
        const user = (
          await fetch(`${location.origin}/api/v1/users/${id}`, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
          })
        ).json();
        return user;
      },
      createUser: async (user) => {
        const newUser = (
          await fetch(`${location.origin}/api/v1/users`, {
            method: "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
            body: JSON.stringify(user),
          })
        ).json();
        return newUser;
      },
      updateUser: async (user) => {
        const updatedUser = (
          await fetch(`${location.origin}/api/v1/users/${user.id}`, {
            method: "PUT",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
            body: JSON.stringify(user),
          })
        ).json();
        return updatedUser;
      },
      deleteUser: async (id) => {
        const deletedUser = (
          await fetch(`${location.origin}/api/v1/users/${id}`, {
            method: "DELETE",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
          })
        ).json();
        return deletedUser;
      },
    },
  },
  data: {
    geo: {
      frlepf: async (q = "fives") => {
        const data = (
          await fetch(`${location.origin}/api/v1/data/geo/fr-lepf?q=${q}`, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
          })
        ).json();
        return data;
      },
      gacwp: async (lon, lat, km = 10) => {
        const data = (
          await fetch(
            `${location.origin}/api/v1/data/geo/gacwp?lon=${lon}&lat=${lat}&km=${km}`,
            {
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
              },
            }
          )
        ).json();
        return data;
      },
    },
  },
  tools: {
    simulateConfidenceMonteCarlo: (
      confidenceRating,
      minutesRemaining = 480,
      iterations = 10000,
      riskThreshold = 0.2
    ) => {
      /**
       * Simulation de Monte Carlo du risque opérationnel d’un site
       * @param {Object} confidenceRating - Grille complète des indicateurs (internal & external)
       * @param {number} minutesRemaining - Nombre de minutes à simuler (ex: 480 = 8h)
       * @param {number} iterations - Nombre de runs de simulation (plus = plus précis)
       * @param {number} riskThreshold - Score de risque pondéré max toléré avant de considérer la journée comme "échouée"
       * @returns {Object} Résultat avec probabilité de succès, échec, détail
       */
      let failedRuns = 0;

      const allIndicators = [
        ...(confidenceRating.internalIndicator || []),
        ...(confidenceRating.externalIndicator || []),
      ];

      for (let run = 0; run < iterations; run++) {
        let failed = false;

        for (let minute = 0; minute < minutesRemaining; minute++) {
          let minuteRiskScore = 0;

          for (const { score, weighting } of allIndicators) {
            if (Math.random() > score) {
              minuteRiskScore += weighting;
            }
          }

          if (minuteRiskScore > riskThreshold) {
            failed = true;
            break; // inutile de simuler la suite pour cette run
          }
        }

        if (failed) failedRuns++;
      }

      const successRate = (iterations - failedRuns) / iterations;
      const failureRate = failedRuns / iterations;

      return {
        successProbability: +(successRate * 100).toFixed(2), // %
        failureProbability: +(failureRate * 100).toFixed(2), // %
        iterations,
        minutesSimulated: minutesRemaining,
        riskThreshold,
      };
    },
    calculateConfidenceScore: (confidenceRating) => {
      /**
       * Calcule le score de confiance global d’un site
       * @param {Object} confidenceRating - JSON avec internalIndicator[] et externalIndicator[]
       * @returns {number} - Score de confiance entre 0 et 100 (arrondi)
       */
      const indicators = [
        ...(confidenceRating.internalIndicator || []),
        ...(confidenceRating.externalIndicator || []),
      ];

      let total = 0;

      for (const { score, weighting } of indicators) {
        total += score * weighting;
      }

      return total.toPrecision(3);
    },
    intentConfidence: (score) => {
      if (score <= 3) {
        return "danger";
      } else if (score <= 5) {
        return "warning";
      } else if (score <= 7) {
        return "primary";
      } else if (score <= 9) {
        return "success";
      } else {
        return "none";
      }
    },
  },
  demo: {
    async: {
      listIssues: async () => {
        const issues = (
          await fetch(`${location.origin}/api/v1/issues?mode=demo`, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
          })
        ).json();
        return issues;
      },
      createIssue: async (issue) => {
        const newIssue = (
          await fetch(`${location.origin}/api/v1/issues?mode=demo`, {
            method: "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
            body: JSON.stringify(issue),
          })
        ).json();
        return newIssue;
      },
    },
  },
};
